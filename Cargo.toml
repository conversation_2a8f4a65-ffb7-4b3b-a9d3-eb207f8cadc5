[package]
name = "newsletter"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow = "1.0.98"
axum = "0.8.4"
config = "0.15.11"
derive_builder = "0.20.2"
serde = { version = "1.0.219", features = ["derive"] }
strum = { version = "0.27.1", features = ["derive"] }
thiserror = "2.0.12"
tokio = { version = "1.45.1", features = ["macros", "rt-multi-thread"] }
tracing = "0.1.41"
tracing-appender = "0.2.3"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }

[dev-dependencies]
thiserror = "2.0.12"
