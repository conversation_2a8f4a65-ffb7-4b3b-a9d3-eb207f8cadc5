use anyhow::Result;
use tracing::level_filters::LevelFilter;
use tracing_appender::{
    non_blocking::{NonBlocking, WorkerGuard},
    rolling::{RollingFileAppender, Rotation},
};
use tracing_subscriber::{
    fmt::{
        format::{Compact, Format, DefaultFields},
        Layer,
    },
    layer::SubscriberExt,
    registry::Registry,
    EnvFilter,
};
use derive_builder::Builder;

type FileLayer = Layer<Registry, DefaultFields, Format<Compact>, NonBlocking>;
type StdoutLayer = Layer<Registry, DefaultFields, Format, NonBlocking>;

#[derive(Builder, Debug)]
#[builder(setter(into), pattern = "owned")]
struct FileLayer2<'a> {
    log_dir: &'a str,
    filename: &'a str,
    #[builder(default = "LevelFilter::INFO")]
    level: LevelFilter,
    #[builder(default = "Rotation::DAILY")]
    rotation: Rotation,
    format: Format,
}

impl<'a> FileLayer2<'a> {
    fn create_file_layer(&self) -> (WorkerGuard, FileLayer) {
        let appender = RollingFileAppender::new(self.rotation, self.log_dir, self.filename);
        let (non_blocking, guard) = tracing_appender::non_blocking(appender);
        let layer = Layer::new()
            .event_format(self.format.clone())
            .with_writer(non_blocking)
            .with_filter(self.level)
            .compact();
        (guard, layer)
    }
}

fn create_stdout_layer(format: &Format) -> (WorkerGuard, StdoutLayer) {
    let (stdout, guard) = tracing_appender::non_blocking(std::io::stdout());
    let layer = Layer::new()
        .event_format(format.clone())
        .with_writer(stdout)
        .with_ansi(true);
    (guard, layer)
}

pub fn init_tracing(log_dir: &str, level: LevelFilter) -> Result<Vec<WorkerGuard>> {
    let mut guards = Vec::new();

    let format = Format::default()
        .with_line_number(true)
        .with_file(true)
        .with_target(false);

    let (stdout_guard, stdout_layer) = create_stdout_layer(&format);
    guards.push(stdout_guard);

    // 可以配置多个文件日志层，比如 info.log 和 error.log
    let file_configs = vec![
        FileLayer2Builder::default()
            .log_dir(log_dir)
            .filename("info.log")
            .level(LevelFilter::INFO)
            .format(format.clone())
            .build()
            .unwrap(),
        FileLayer2Builder::default()
            .log_dir(log_dir)
            .filename("error.log")
            .level(LevelFilter::ERROR)
            .format(format.clone())
            .build()
            .unwrap(),
    ];

    let mut file_layers = Vec::new();

    for conf in file_configs {
        let (guard, layer) = conf.create_file_layer();
        guards.push(guard);
        file_layers.push(layer);
    }

    // 将所有日志层用 and_then 组合起来
    let combined_file_layer = file_layers.into_iter().reduce(|a, b| a.and_then(b));

    // 构建最终 subscriber
    let subscriber = match combined_file_layer {
        Some(file_layer) => tracing_subscriber::registry()
            .with(stdout_layer)
            .with(file_layer)
            .with(EnvFilter::from_default_env().add_directive(level.into())),
        None => tracing_subscriber::registry()
            .with(stdout_layer)
            .with(EnvFilter::from_default_env().add_directive(level.into())),
    };

    subscriber.init();

    Ok(guards)
}
