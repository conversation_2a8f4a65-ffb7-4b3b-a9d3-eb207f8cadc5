use anyhow::Result;
use tracing::level_filters::LevelFilter;
use tracing_appender::{
    non_blocking::{NonBlocking, WorkerGuard},
    rolling::{RollingFileAppender, Rotation},
};
use tracing_subscriber::{
    EnvFilter, Layer as _, Registry,
    fmt::{
        Layer,
        format::{Comp<PERSON>, DefaultFields, Format},
        writer::MakeWriterExt,
    },
    layer::SubscriberExt,
    util::SubscriberInitExt,
};

// 类型别名简化复杂的类型签名
type StdoutLayer = Layer<Registry, DefaultFields, Format, NonBlocking>;
type FileLayer = Layer<Registry, DefaultFields, Format<Compact>, NonBlocking>;

pub fn tracing_init() -> Result<Vec<WorkerGuard>> {
    tracing_init_with_config("logs/", LevelFilter::INFO)
}

pub fn tracing_init_with_config(log_dir: &str, level: LevelFilter) -> Result<Vec<WorkerGuard>> {
    let mut guards = Vec::new();

    let format = create_format();

    let (stdout_guard, stdout_layer) = create_stdout_layer(&format);
    let (file_guard, file_layer) = create_file_layer(log_dir, &format);

    guards.extend([stdout_guard, file_guard]);

    let env_filter = EnvFilter::from_default_env().add_directive(level.into());
    tracing_subscriber::registry()
        .with(stdout_layer.and_then(file_layer))
        .with(env_filter)
        .init();

    Ok(guards)
}

fn create_format() -> Format {
    Format::default()
        .with_line_number(true)
        .with_file(true)
        .with_target(false)
}

fn create_stdout_layer(format: &Format) -> (WorkerGuard, StdoutLayer) {
    let (stdout, guard) = tracing_appender::non_blocking(std::io::stdout());
    let layer = Layer::new()
        .event_format(format.clone())
        .with_writer(stdout)
        .with_ansi(true);
    (guard, layer)
}

pub fn init_tracing(log_dir: &str, level: LevelFilter) -> Result<Vec<WorkerGuard>> {
    let mut guards = Vec::new();

    let format = Format::default()
        .with_line_number(true)
        .with_file(true)
        .with_target(false);

    let (stdout_guard, stdout_layer) = create_stdout_layer(&format);
    guards.push(stdout_guard);

    // 可以配置多个文件日志层，比如 info.log 和 error.log
    let file_configs = vec![
        FileLayer2Builder::default()
            .log_dir(log_dir)
            .filename("info.log")
            .level(LevelFilter::INFO)
            .format(format.clone())
            .build()
            .unwrap(),
        FileLayer2Builder::default()
            .log_dir(log_dir)
            .filename("error.log")
            .level(LevelFilter::ERROR)
            .format(format.clone())
            .build()
            .unwrap(),
    ];

    let mut file_layers = Vec::new();

    for conf in file_configs {
        let (guard, layer) = conf.create_file_layer();
        guards.push(guard);
        file_layers.push(layer);
    }

    // 将所有日志层用 and_then 组合起来
    let combined_file_layer = file_layers.into_iter().reduce(|a, b| a.and_then(b));

    // 构建最终 subscriber
    let subscriber = match combined_file_layer {
        Some(file_layer) => tracing_subscriber::registry()
            .with(stdout_layer)
            .with(file_layer)
            .with(EnvFilter::from_default_env().add_directive(level.into())),
        None => tracing_subscriber::registry()
            .with(stdout_layer)
            .with(EnvFilter::from_default_env().add_directive(level.into())),
    };

    subscriber.init();

    Ok(guards)
}
